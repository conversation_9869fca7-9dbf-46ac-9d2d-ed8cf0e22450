#!/usr/bin/env python3
"""
Simple test runner to execute the key parts of the loan calculation
and verify the fixes work correctly.
"""

import numpy as np
import pandas as pd
from datetime import datetime

# Configuration
projected_date = '29/03/2035'
repayment_start_date = '11/18/2024'
payments_csv_path = 'sample_payments.csv'

# Loan data
dic = {
    'Principal' :         [3500.00,     6000.00,     4500.00,     6000.00,     4500.00,     6000.00,     5500.00,     3296.00,     3704.00     ],
    'Interest_Rate' :     [2.750,       2.750,       3.730,       3.730,       4.990,       4.990,       5.500,       5.500,       5.500       ],
    'Loan_Type' :         ['Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Unsub'     ],
    'Disbursement_Date' : ['08/28/2020','08/28/2020','08/24/2021','08/24/2021','08/29/2022','08/29/2022','08/29/2023','08/30/2023','01/04/2024'],
}

def calculate_interest(interest_rate, current_principal, start_date, current_date):
    """
    Calculate daily interest accumulation for a loan.
    """
    if current_principal <= 0:
        return 0.0
    
    # Calculate daily interest: (annual_rate / 100) / 365 * principal
    daily_interest = round((interest_rate / 100 / 365) * current_principal, 2)
    
    return daily_interest

def main():
    print("=== TESTING CORRECTED LOAN CALCULATION ===")
    print()
    
    # Create DataFrame
    df = pd.DataFrame(dic)
    df.insert(0, 'Loan #', range(1,10))
    df.insert(1, 'Original Principal', df['Principal'])
    df.insert(3, 'Interest', [0.0] * 9)
    df.insert(4, 'Total', df['Principal'] + df['Interest'])
    df["Disbursement_Date"] = df["Disbursement_Date"].apply(lambda x : pd.Timestamp(x))
    
    print("Initial loan data:")
    print(df[['Loan #', 'Principal', 'Interest_Rate', 'Loan_Type', 'Disbursement_Date']])
    print()
    
    # Load payments data
    try:
        payments_df = pd.read_csv(payments_csv_path)
        payments_df['LoanName'] = payments_df['LoanName'].apply(lambda x : int(x.split('Loan')[1].split(' ')[0]))
        payments_df["Date"] = payments_df["Date"].apply(lambda x : pd.Timestamp(x))
        
        print(f"Loaded {len(payments_df)} payment records")
        print("Sample payment records:")
        print(payments_df.head())
        print()
        
    except FileNotFoundError:
        print("Sample payments file not found. Run test_loan_calculation.py first.")
        return
    
    # Test calculation for one loan (Loan 1)
    loan_idx = 0  # Loan 1
    loan_info = df.iloc[loan_idx]
    
    print(f"=== TESTING LOAN {loan_idx + 1} CALCULATION ===")
    print(f"Type: {loan_info['Loan_Type']}")
    print(f"Original Principal: ${loan_info['Principal']:,.2f}")
    print(f"Interest Rate: {loan_info['Interest_Rate']}%")
    print(f"Disbursement: {loan_info['Disbursement_Date'].strftime('%Y-%m-%d')}")
    print()
    
    # Get payments for this loan
    loan_payments = payments_df[payments_df['LoanName'] == loan_idx + 1].sort_values('Date')
    print(f"Found {len(loan_payments)} payments for this loan:")
    for _, payment in loan_payments.iterrows():
        print(f"  {payment['Date'].strftime('%Y-%m-%d')}: ${payment['Total']:,.2f} (Balance: ${payment.get('UnpaidPrincipalBalanceValue', 'N/A')})")
    print()
    
    # Simulate calculation for a few key dates
    repayment_start_date_dt = pd.Timestamp(repayment_start_date)
    
    principal = loan_info['Principal']
    interest = 0.0
    
    print("=== SIMULATION ===")
    print(f"Starting: Principal=${principal:,.2f}, Interest=${interest:.2f}")
    
    # Test interest accrual
    if loan_info['Loan_Type'] == 'Sub':
        print("Subsidized loan - interest starts at repayment date")
        # Calculate interest from repayment start to first payment
        days_to_first_payment = (pd.Timestamp('2024-11-22') - repayment_start_date_dt).days
        daily_interest = calculate_interest(loan_info['Interest_Rate'], principal, None, None)
        total_interest = daily_interest * days_to_first_payment
        interest += total_interest
        print(f"Interest accrued over {days_to_first_payment} days: ${total_interest:.2f}")
    else:
        print("Unsubsidized loan - interest starts at disbursement")
        # Calculate interest from disbursement to first payment
        days_to_first_payment = (pd.Timestamp('2024-11-22') - loan_info['Disbursement_Date']).days
        daily_interest = calculate_interest(loan_info['Interest_Rate'], principal, None, None)
        total_interest = daily_interest * days_to_first_payment
        interest += total_interest
        print(f"Interest accrued over {days_to_first_payment} days: ${total_interest:.2f}")
    
    print(f"Before first payment: Principal=${principal:,.2f}, Interest=${interest:.2f}, Total=${principal + interest:,.2f}")
    
    # Apply first payment
    first_payment = loan_payments.iloc[0]
    payment_amount = abs(first_payment['Total'])
    
    print(f"Applying payment of ${payment_amount:.2f}")
    
    # Apply payment: first to interest, then to principal
    if interest > 0:
        if payment_amount >= interest:
            payment_amount -= interest
            interest = 0.0
            print(f"  Paid all interest, remaining payment: ${payment_amount:.2f}")
        else:
            interest -= payment_amount
            payment_amount = 0.0
            print(f"  Paid partial interest, remaining interest: ${interest:.2f}")
    
    if payment_amount > 0 and principal > 0:
        if payment_amount >= principal:
            principal = 0.0
            print(f"  Paid off all principal")
        else:
            principal -= payment_amount
            print(f"  Reduced principal by ${payment_amount:.2f}")
    
    print(f"After payment: Principal=${principal:,.2f}, Interest=${interest:.2f}, Total=${principal + interest:,.2f}")
    
    # Compare with actual balance if available
    if not pd.isna(first_payment.get('UnpaidPrincipalBalanceValue')):
        actual_balance = first_payment['UnpaidPrincipalBalanceValue']
        calculated_balance = principal
        difference = abs(calculated_balance - actual_balance)
        
        print(f"\n=== VALIDATION ===")
        print(f"Actual principal balance: ${actual_balance:,.2f}")
        print(f"Calculated principal: ${calculated_balance:,.2f}")
        print(f"Difference: ${difference:.2f}")
        
        if difference < 1.0:
            print("✅ CALCULATION MATCHES ACTUAL RECORDS!")
        else:
            print("❌ Calculation does not match - needs further adjustment")
    
    print("\n=== TEST COMPLETE ===")

if __name__ == "__main__":
    main()
