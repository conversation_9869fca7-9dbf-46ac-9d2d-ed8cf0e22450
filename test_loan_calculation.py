#!/usr/bin/env python3
"""
Test script to verify the loan calculation fixes work correctly.
This creates a sample payments CSV and runs the calculation to check for accuracy.
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_sample_payments_csv():
    """Create a sample payments CSV file for testing"""
    
    # Sample payment data based on the structure shown in the notebook
    payments_data = [
        # Recent payments (negative amounts indicate payments made)
        ['2025-06-20', 'Loan7 Subsidized', 'Subsidized', -990.22, -9.78, 0.00, -1000.00, 4248.53],
        ['2025-06-20', 'Loan4 Unsubsidized', 'Unsubsidized', -993.81, -6.19, 0.00, -1000.00, 4004.98],
        ['2025-06-20', 'Loan6 Unsubsidized', 'Unsubsidized', -991.09, -8.91, 0.00, -1000.00, 4296.21],
        ['2025-06-07', 'Loan1 Subsidized', 'Subsidized', -26.29, -7.08, 0.00, -33.37, 3313.75],
        ['2025-06-07', 'Loan2 Unsubsidized', 'Unsubsidized', -48.71, -10.40, 0.00, -59.11, 4859.45],
        
        # Monthly payments (pattern from 2024-11-22)
        ['2024-11-22', 'Loan1 Subsidized', 'Subsidized', -26.29, -7.08, 0.00, -33.37, 3340.04],
        ['2024-11-22', 'Loan2 Unsubsidized', 'Unsubsidized', -48.71, -10.40, 0.00, -59.11, 4908.16],
        ['2024-11-22', 'Loan3 Subsidized', 'Subsidized', -31.25, -2.12, 0.00, -33.37, 4468.75],
        ['2024-11-22', 'Loan4 Unsubsidized', 'Unsubsidized', -27.18, -6.19, 0.00, -33.37, 4998.79],
        ['2024-11-22', 'Loan5 Subsidized', 'Subsidized', -31.25, -2.12, 0.00, -33.37, 4468.75],
        ['2024-11-22', 'Loan6 Unsubsidized', 'Unsubsidized', -25.46, -7.91, 0.00, -33.37, 5287.30],
        ['2024-11-22', 'Loan7 Subsidized', 'Subsidized', -31.25, -2.12, 0.00, -33.37, 5238.75],
        ['2024-11-22', 'Loan8 Unsubsidized', 'Unsubsidized', -25.46, -7.91, 0.00, -33.37, 3270.46],
        ['2024-11-22', 'Loan9 Unsubsidized', 'Unsubsidized', -25.46, -7.91, 0.00, -33.37, 3678.54],
        
        # Original disbursements (positive amounts)
        ['2024-01-04', 'Loan9 Unsubsidized', 'Unsubsidized', 3704.00, 0.00, 0.00, 3704.00, np.nan],
        ['2023-08-30', 'Loan8 Unsubsidized', 'Unsubsidized', 3296.00, 0.00, 0.00, 3296.00, np.nan],
        ['2023-08-29', 'Loan7 Subsidized', 'Subsidized', 5500.00, 0.00, 0.00, 5500.00, np.nan],
        ['2022-08-29', 'Loan6 Unsubsidized', 'Unsubsidized', 6000.00, 0.00, 0.00, 6000.00, np.nan],
        ['2022-08-29', 'Loan5 Subsidized', 'Subsidized', 4500.00, 0.00, 0.00, 4500.00, np.nan],
        ['2021-08-24', 'Loan4 Unsubsidized', 'Unsubsidized', 6000.00, 0.00, 0.00, 6000.00, np.nan],
        ['2021-08-24', 'Loan3 Subsidized', 'Subsidized', 4500.00, 0.00, 0.00, 4500.00, np.nan],
        ['2021-01-25', 'Loan2 Unsubsidized', 'Unsubsidized', 3000.00, 0.00, 0.00, 3000.00, np.nan],
        ['2021-01-25', 'Loan1 Subsidized', 'Subsidized', 1750.00, 0.00, 0.00, 1750.00, np.nan],
        ['2020-08-28', 'Loan2 Unsubsidized', 'Unsubsidized', 3000.00, 0.00, 0.00, 3000.00, np.nan],
        ['2020-08-28', 'Loan1 Subsidized', 'Subsidized', 1750.00, 0.00, 0.00, 1750.00, np.nan],
    ]
    
    columns = ['Date', 'LoanName', 'LoanType', 'Principal', 'Interest', 'Fees', 'Total', 'UnpaidPrincipalBalanceValue']
    
    df = pd.DataFrame(payments_data, columns=columns)
    df['Date'] = pd.to_datetime(df['Date'])
    
    # Sort by date descending (most recent first)
    df = df.sort_values('Date', ascending=False)
    
    # Save to CSV
    df.to_csv('sample_payments.csv', index=False)
    print("Created sample_payments.csv for testing")
    
    return df

def test_interest_calculation():
    """Test the corrected interest calculation function"""
    
    # Test the calculate_interest function
    def calculate_interest(interest_rate, current_principal, start_date, current_date):
        if current_principal <= 0:
            return 0.0
        daily_interest = round((interest_rate / 100 / 365) * current_principal, 2)
        return daily_interest
    
    # Test cases
    test_cases = [
        (5.5, 1000.0, "Daily interest for $1000 at 5.5%"),
        (2.75, 3500.0, "Daily interest for $3500 at 2.75%"),
        (0.0, 1000.0, "Daily interest for $1000 at 0%"),
        (5.5, 0.0, "Daily interest for $0 at 5.5%"),
    ]
    
    print("=== TESTING INTEREST CALCULATION ===")
    for rate, principal, description in test_cases:
        daily_interest = calculate_interest(rate, principal, None, None)
        annual_interest = daily_interest * 365
        print(f"{description}: ${daily_interest:.2f}/day, ${annual_interest:.2f}/year")
    print()

if __name__ == "__main__":
    print("Testing loan calculation fixes...")
    print()
    
    # Create sample data
    sample_df = create_sample_payments_csv()
    print(f"Sample data has {len(sample_df)} records")
    print()
    
    # Test interest calculation
    test_interest_calculation()
    
    print("Test completed. Now run the notebook with the sample data to verify fixes.")
