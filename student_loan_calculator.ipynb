projected_date = '29/03/2034'
repayment_start_date = '11/18/2024'
# Use sample data for testing - change this back to your actual file path
payments_csv_path = '/Users/<USER>/Downloads/AllLoans.csv'  # '/Users/<USER>/Downloads/AllLoans.csv'

### EXAMPLE INPUT DICTIONARY: ###

'''
Disbursement Date Program Outstanding Principal
Balance Accrued Interest Interest Rate Current Total Balance
08/28/20 DLSUB $ 3,500.00 $ 0.00 2.750 $ 3,500.00
08/28/20 DLUNSUB $ 6,000.00 $ 96.20 2.750 $ 6,096.20
08/24/21 DLSUB $ 4,500.00 $ 0.00 3.730 $ 4,500.00
08/24/21 DLUNSUB $ 6,000.00 $ 130.49 3.730 $ 6,130.49
08/29/22 DLSUB $ 4,500.00 $ 0.00 4.990 $ 4,500.00
08/29/22 DLUNSUB $ 6,000.00 $ 174.59 4.990 $ 6,174.59
08/29/23 DLSUB $ 5,500.00 $ 0.00 5.500 $ 5,500.00
08/30/23 DLUNSUB $ 3,296.00 $ 69.47 5.500 $ 3,365.47
01/04/24 DLUNSUB $ 3,704.00 $ 43.22 5.500 $ 3,747.22
'''

"""


dic = {

#                          Loan1        Loan2        Loan3        Loan4        Loan5        Loan6        Loan7        Loan8        Loan9

    'Principal' :         [3500.00,     6000.00,     4500.00,     6000.00,     4500.00,     6000.00,     5500.00,     3296.00,     3704.00     ],
    'Interest_Rate' :     [2.750,       2.750,       3.730,       3.730,       4.990,       4.990,       5.500,       5.500,       5.500       ],
    'Loan_Type' :         ['Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Unsub'     ],
    'Disbursement_Date' : ['08/28/2020','08/28/2020','08/24/2021','08/24/2021','08/29/2022','08/29/2022','08/29/2023','08/30/2023','01/04/2024']

    }



"""

### FILL IN YOUR INFORMATION: ###

dic = {

#                          Loan1        Loan2        Loan3        Loan4        Loan5        Loan6        Loan7        Loan8        Loan9

    'Principal' :         [3500.00,     6000.00,     4500.00,     6000.00,     4500.00,     6000.00,     5500.00,     3296.00,     3704.00     ],
    'Interest_Rate' :     [2.750,       2.750,       3.730,       3.730,       4.990,       4.990,       5.500,       5.500,       5.500       ],
    'Loan_Type' :         ['Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Sub',       'Unsub',     'Unsub'     ],
    'Disbursement_Date' : ['08/28/2020','08/28/2020','08/24/2021','08/24/2021','08/29/2022','08/29/2022','08/29/2023','08/30/2023','01/04/2024'],


      }



import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.dates as dates                                                # To plot and display a date range on the x axis
from datetime import datetime as dt                                             # Magic.
import plotly.graph_objects as go
pd.options.display.float_format = '{:,.2f}'.format                              # Add thousands commas to y-axis ticks
pd.options.mode.chained_assignment = None  # default='warn'

df = pd.DataFrame(dic)                                                          # Create Pandas dataframe from dictionary
df.insert(0, 'Loan #', range(1,10))
df.insert(1, 'Original Prinicipal', df['Principal'])
df.insert(3, 'Interest', [0.0] * 9)
df.insert(4, 'Total', df['Principal'] + df['Interest'])
df["Disbursement_Date"] = df["Disbursement_Date"].apply(lambda x : pd.Timestamp(x))

df

payments_df = pd.read_csv(payments_csv_path)
payments_df = payments_df[payments_df['Description'] == 'PAYMENT']

# Handle different CSV formats
if '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">Date' in payments_df.columns:
    # Original format
    payments_df.rename(columns={'<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">Date':'Date'}, inplace = True)
    payments_df.drop(labels = ['Unnamed: 8', 'Description'], axis = 1, inplace = True)
    
    payments_df['Principal'] = pd.to_numeric(payments_df['Principal'].replace('\$|,', '', regex=True))
    payments_df['Interest'] = pd.to_numeric(payments_df['Interest'].replace('\$|,', '', regex=True))
    payments_df['Fees'] = pd.to_numeric(payments_df['Fees'].replace('\$|,', '', regex=True))
    payments_df['Total'] = pd.to_numeric(payments_df['Total'].replace('\$|,', '', regex=True))
    payments_df['UnpaidPrincipalBalanceValue'] = pd.to_numeric(payments_df['UnpaidPrincipalBalanceValue'].replace('\$|,', '', regex=True), errors='coerce')
    
    payments_df.insert(2, 'LoanType', payments_df['LoanName'].apply(lambda x : x.split(' ')[-1]))
    payments_df['LoanName'] = payments_df['LoanName'].apply(lambda x : x.split(' ')[0][-1]).astype(int)
else:
    # Sample/test format - data is already clean
    payments_df['LoanName'] = payments_df['LoanName'].apply(lambda x : int(x.split('Loan')[1].split(' ')[0]))

payments_df["Date"] = payments_df["Date"].apply(lambda x : pd.Timestamp(x))

payments_df.dropna(inplace=True)
payments_df                                                                              # This should represent your input

# payments_df = pd.concat([pd.DataFrame([[ pd.Timestamp('2025-01-03'), 2, 'Unsubsidized', -516.42, -283.58, 0.00, -1000.00, 5443.72 - 1000]], columns=payments_df.columns), payments_df], ignore_index=True)

reoccurring_payment_df = payments_df[payments_df['Date'] == pd.Timestamp('06/07/2025')]

for i in range(12*12):

    tmp_df = reoccurring_payment_df[reoccurring_payment_df['Date'] == reoccurring_payment_df['Date'].values[0]]

    next_months_payments_df = tmp_df['Date'] + pd.DateOffset(weeks = 4)

    tmp_df['Date'] = next_months_payments_df

    reoccurring_payment_df = pd.concat([tmp_df, reoccurring_payment_df], ignore_index=True)

payments_df = pd.concat([reoccurring_payment_df, payments_df], ignore_index=True).drop_duplicates()

def calculate_interest(interest_rate, current_principal, start_date, current_date):
    """
    Calculate daily interest accumulation for a loan.
    
    Args:
        interest_rate: Annual interest rate as percentage (e.g., 5.5 for 5.5%)
        current_principal: Current principal balance
        start_date: Date when interest started accruing (not used in daily calc)
        current_date: Current date (not used in daily calc)
    
    Returns:
        Daily interest amount
    """
    if current_principal <= 0:
        return 0.0
    
    # Calculate daily interest: (annual_rate / 100) / 365 * principal
    daily_interest = round((interest_rate / 100 / 365) * current_principal, 2)
    
    return daily_interest

earliest_disbursement_date_dt = min(df['Disbursement_Date'])                    # Find the earliest date to plot
repayment_start_date_dt = pd.Timestamp(repayment_start_date)                    # Turn everything into datettime objects
end_pay_range_dt = pd.Timestamp(projected_date)
number_of_loans = len(dic['Principal'])
number_of_days = (end_pay_range_dt-earliest_disbursement_date_dt).days +1

x = pd.date_range(earliest_disbursement_date_dt,
                  end_pay_range_dt,
                  periods=number_of_days)                                       # Initialize domain that contains a point for every day
y = np.zeros((number_of_loans, number_of_days))                                 # Initialize array that holds the cost of every loan for every day (points to be plotted)

maxes = []                                                                      # Initialize list to hold the max values of each loan
increases = []                                                                  # Initialize list to hold % change
first_x = []                                                                    # Initialize x values for each disbursement date
first_y = []                                                                    # Initialize y values for each disbursement date

for j in range(number_of_loans):                                                # For all loans

    (principal,
     interest,
     interest_rate,
     loan_type,
     disbursement_date) = (df.iloc[j]['Principal'],
                           df.iloc[j]['Interest'],
                           df.iloc[j]['Interest_Rate'],
                           df.iloc[j]['Loan_Type'],
                           df.iloc[j]['Disbursement_Date'])                           # Unpack loan info
        
    loan_payments_df = payments_df[payments_df['LoanName'] == j + 1]
    
    # Debug: Print loan info
    print(f"Processing Loan {j+1}: {loan_type}, Principal: ${principal:,.2f}, Rate: {interest_rate}%")
    print(f"  Disbursement: {disbursement_date.strftime('%Y-%m-%d')}")
    print(f"  Found {len(loan_payments_df)} payment records")

    for i in range(number_of_days):                                             # For every day (besides the first)

        accum_interest = 0

        if x[i] > disbursement_date:                                          # If the loan has been disbursed already

            if loan_type == 'Sub':                                              # If the data is a subsidized loan

                if x[i] >= repayment_start_date_dt:                             # If repayments have come into effect

                    accum_interest = calculate_interest(interest_rate,                # Calculate the interest
                                                  principal,
                                                  repayment_start_date_dt,
                                                  x[i])
                    
            else:                                                               # Else, the loan is unsubsidized and has started accruing interest immediately

                accum_interest = calculate_interest(interest_rate,
                                              principal,
                                              disbursement_date,
                                              x[i])
                
            if len(first_x) == j:

                first_x.append(x[i])                                            # Check to see if this is the disbursement date, then save x and y to make dots on plot
                first_y.append(principal)

            loan_payments_day_df = loan_payments_df[loan_payments_df['Date'] == x[i]]

            if len(loan_payments_day_df) > 0:

                for _, payment_row in loan_payments_day_df.iterrows():
                    # Payment amounts are negative in the CSV, so we need to make them positive
                    payment_amount = abs(payment_row['Total'])
                    
                    if payment_amount > 0 and (principal > 0 or interest > 0):
                        # Apply payment: first to interest, then to principal
                        if interest > 0:
                            if payment_amount >= interest:
                                # Payment covers all interest and some principal
                                payment_amount -= interest
                                interest = 0.0
                            else:
                                # Payment only covers part of interest
                                interest -= payment_amount
                                payment_amount = 0.0
                        
                        # Apply remaining payment to principal
                        if payment_amount > 0 and principal > 0:
                            if payment_amount >= principal:
                                principal = 0.0
                            else:
                                principal -= payment_amount


            interest += accum_interest
            
            y[j,i] = principal + interest

            df.at[j, 'Principal'] = principal
            df.at[j, 'Interest'] = interest
            df.at[j, 'Total'] = y[j,i]

        else:

            y[j,i] = np.nan                                                     # Else, the loan hasn't been disbursed yet. Using nan values leaves them off the plot

    maxes.append(np.nanmax(y[j]))                                               # Save the max value of the loan, exclude nan values
    increase = 100*(y[j,-1] - df.iloc[j]['Total'])/y[j,-1]                            # Calculate and save % increase
    increases.append(increase)

total_increase_days = np.empty_like(y[0])
for i in range(len(total_increase_days)):
    total_increase_days[i] = np.sum(y[:,i])

max = np.max(maxes)                                                             # Find the absolute maximum of all plots to render the vertical repayment start date line
total_principal = df['Principal'].sum()
total_interest = df['Interest'].sum()
sum_total = df['Total'].sum()
total_owed = sum(y[:,-1])
total_increase = 100 * (total_owed - sum_total) / total_owed

# Validation: Compare calculated balances with actual payment records
print("=== VALIDATION AGAINST ACTUAL PAYMENT RECORDS ===")
print()

# Get the most recent payment record for each loan to compare balances
validation_errors = []

for loan_num in range(1, 10):
    loan_payments = payments_df[payments_df['LoanName'] == loan_num]
    
    if len(loan_payments) > 0:
        # Get the most recent payment record with a valid balance
        recent_payments = loan_payments[loan_payments['UnpaidPrincipalBalanceValue'].notna()]
        
        if len(recent_payments) > 0:
            most_recent = recent_payments.iloc[0]  # First row is most recent due to sorting
            actual_balance = most_recent['UnpaidPrincipalBalanceValue']
            calculated_principal = df.iloc[loan_num-1]['Principal']
            calculated_total = df.iloc[loan_num-1]['Total']
            
            print(f"Loan {loan_num} (as of {most_recent['Date'].strftime('%Y-%m-%d')}):")
            print(f"  Actual Principal Balance: ${actual_balance:,.2f}")
            print(f"  Calculated Principal: ${calculated_principal:,.2f}")
            print(f"  Calculated Total: ${calculated_total:,.2f}")
            
            # Check if calculated principal is close to actual balance
            diff = abs(calculated_principal - actual_balance)
            if diff > 1.0:  # Allow for small rounding differences
                validation_errors.append(f"Loan {loan_num}: Difference of ${diff:,.2f}")
                print(f"  ❌ MISMATCH: Difference of ${diff:,.2f}")
            else:
                print(f"  ✅ MATCH: Difference of ${diff:,.2f}")
            print()

if validation_errors:
    print("\n⚠️  VALIDATION ERRORS FOUND:")
    for error in validation_errors:
        print(f"  - {error}")
else:
    print("\n✅ All loan calculations match actual payment records!")

tsys = []

fig = go.Figure()

fig.add_vline(x=repayment_start_date_dt, line_dash="dash", line_color="red")

for i in range(9):

    fig.add_trace(go.Scatter(name = f'Loan #{i+1}',
                             mode="lines", 
                             x = x, 
                             y = y[i]))

fig.add_trace(go.Scatter(name = f'Total Owed',
                             mode="lines", 
                             x = x, 
                             y = total_increase_days))

fig.update_layout(
    
    autosize = False,
    width = 1000,
    height = 500,
    # title = title,
    # xaxis_title = x_title,
    # yaxis_title = y_title,
    template = 'plotly_dark'
)

fig.update_yaxes(rangemode="tozero")

df

fig, ax = plt.subplots(1,figsize=(9,5.5),dpi=145)                               # Increase DPI for style points
fig.autofmt_xdate()                                                             # Magic.
xfmt = dates.DateFormatter('%d-%m-%y')                                          # Format of the timestamp on the x-axis ticks
ax.xaxis.set_major_formatter(xfmt)                                              # Implement that format
ax.get_yaxis().set_major_formatter(
    matplotlib.ticker.FuncFormatter(lambda x, p: "$" + format(int(x), ',')))    # Used to format the y axis tick labels (thousands commas, money sign)
times = pd.date_range(earliest_disbursement_date_dt,
                      end_pay_range_dt,
                      periods=15)                                               # For x tick marks. Change 'periods' to add or remove the # of x ticks

for i in range(number_of_loans):                                                # For each loan

    label = f'Loan #{i+1} = ${y[i,-1]:9,.2f}, a {increases[i]:5.1f}% increase'

    p = plt.plot(x, y[i], label = label, alpha = .85)                           # Plot each loan vs. time line (save the object to extract color in next line)
    plt.plot(x[-1], y[i,-1], 'o', c = p[0].get_color())                         # Plot a larger dot for the final point of each line in the same color as the line
    plt.plot(first_x[i], first_y[i], 'o', c = p[0].get_color())

plt.vlines(repayment_start_date_dt, 0, max*1.1,
           'r','--',alpha = .5, label = "Repayment Start Date")                 # Cool matplotlib object that takes an x value, and then a min and max y value to build a vertical line
plt.suptitle("                Interest Accumulation of Student Loans", fontsize = 15)
plt.title(f'Total Owed = ${total_owed:,.2f}, a {total_increase:.1f}% overall change')
plt.grid()
plt.legend(prop={'family':'monospace', 'size': 8},                              # In the legend, using monspace uses all characters of equal width so the float numbers line up vertically
           framealpha=.85,
           title = 'Owed at end of time range:',
           loc = 'lower left')
# plt.xticks(times)                                                               # Uses the time object to the set the number of ticks and their labels
plt.xlabel('Time')
plt.ylabel('$ Owed')
plt.ylim([0, max*1.1])
plt.xlim([pd.Timestamp('06-01-2024'), pd.Timestamp('06-01-2025')])
plt.tight_layout()


loan_projections = []                                                           # Initialize a list to hold all loan projections

_ = np.empty_like(x)                                                            # Initialize a temporary object for the datetime column

for i in range(number_of_days):

    _[i] = (x[i]).date()                                                        # Change all the datetime objects to date objects (gets rid of minutes and seconds)

x = _                                                                           # Don't ask.

for i in range(number_of_loans):                                                # For each loan

    _ = pd.DataFrame({'Date' : x,'Owed' : y[i]})                                # Create a dataframe for the coast of that loan on each day

    loan_projections.append(_)                                                  # And then append it to the masterlist

# check_date = '08/27/2045'
# check_date_dt = dt.strptime(check_date, fmt)                # Turn everything into datettime objects
# loan_projections[0]['Owed'].where(loan_projections[0]['Date'] == check_date_dt).dropna().values[0].round(2)

d = pd.DataFrame({'$' : y[:,-1].round(2)})
d.index += 1                                                                   # Start index at 1
d.index.names = ['Loan #']                                                     # Rename index column
d

fig = plt.figure(figsize = (1.5, 2), dpi = 200)
ax = fig.add_subplot(111)

ax.table(cellText = d.values,
          rowLabels = d.index,
          cellLoc = 'center',
          loc = "center"
         )
ax.set_title(f'Owed on {end_pay_range_dt.date()}')
ax.axis("off");